import React, { useState, useEffect } from 'react';
import { Button, Space, Card, App, Popconfirm, Tag, Tooltip, Drawer } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ClearOutlined, DatabaseOutlined, CheckOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { DBConnection } from '@/types/task';
import { formStyles } from '@/components/SqlMonitor/styles';
import DBConnectionTable from '@/components/SqlMonitor/components/DBConnectionTable';

interface DatabaseConfigTabProps {
  dbConnection: DBConnection | null;
  onDbConnectionChange: (dbConnection: DBConnection | null) => void;
  onAddDbConnection: () => void;
  onEditDbConnection: () => void;
}

/**
 * 数据库连接配置标签页组件
 * 管理数据库连接的配置
 */
const DatabaseConfigTab: React.FC<DatabaseConfigTabProps> = ({ dbConnection, onDbConnectionChange, onAddDbConnection, onEditDbConnection }) => {
  const { message } = App.useApp();

  // 动态高度状态
  const [containerHeight, setContainerHeight] = useState(600);

  // 抽屉状态管理
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedRowFromChild, setSelectedRowFromChild] = useState<DBConnection | null>(null);

  // 计算动态高度
  useEffect(() => {
    const calculateHeight = () => {
      // 100vh - 60(头部) - 84(底部) - 48(card padding: 24*2) - 30(tab切换栏)
      const dynamicHeight = window.innerHeight - 60 - 85 - 48 - 30;
      setContainerHeight(Math.max(400, dynamicHeight)); // 最小高度400px
    };

    // 初次加载时计算
    calculateHeight();

    // 监听窗口大小变化
    window.addEventListener('resize', calculateHeight);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', calculateHeight);
    };
  }, []);

  const handleDeleteDbConnection = () => {
    if (dbConnection) {
      onDbConnectionChange(null);
      message.success(`已移除数据库连接"${dbConnection.name}"`);
    }
  };

  // 处理选择数据库连接按钮点击
  const handleSelectDbConnection = () => {
    setDrawerVisible(true);
  };

  // 处理抽屉关闭
  const handleDrawerClose = () => {
    setDrawerVisible(false);
    setSelectedRowFromChild(null);
  };

  // 处理抽屉内的选择确认
  const handleDrawerSelectionConfirm = () => {
    if (selectedRowFromChild) {
      onDbConnectionChange(selectedRowFromChild);
      handleDrawerClose();
      message.success(`已选择数据库连接"${selectedRowFromChild.name}"`);
    } else {
      message.warning('请先选择一个数据库连接');
    }
  };

  // 处理选择变化（从DBConnectionTable传递过来）
  const handleSelectionChange = (selectedConnections: DBConnection[]) => {
    console.log('DatabaseConfigTab 接收到选择变化:', selectedConnections);
    // 数据库连接是单选，只取第一个
    setSelectedRowFromChild(selectedConnections.length > 0 ? selectedConnections[0] : null);
  };

  // 获取数据库类型标签颜色
  const getDbTypeColor = (dbType: string) => {
    switch (dbType.toLowerCase()) {
      case 'mysql':
        return 'blue';
      case 'oracle':
        return 'orange';
      case 'postgresql':
        return 'green';
      case 'sqlserver':
        return 'purple';
      default:
        return 'default';
    }
  };

  return (
    <div className={`${formStyles.tabContent} relative flex flex-col border border-gray-200 bg-white`} style={{ height: `${containerHeight}px` }}>
      {/* 固定顶部 - 标题和操作按钮 */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-5 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <h4 className="m-0 text-lg font-medium">数据库连接配置</h4>
            <span className="text-gray-500 text-sm">{dbConnection ? '已配置' : '未配置'}</span>
          </div>
          <Space>
            {dbConnection && (
              <Button danger size="small" type="text" icon={<ClearOutlined />} onClick={handleDeleteDbConnection}>
                清空配置
              </Button>
            )}
            <Button size="small" type="text" icon={<PlusOutlined />} onClick={onAddDbConnection}>
              新增连接
            </Button>
            <Button size="small" type="text" icon={<EyeOutlined />} onClick={handleSelectDbConnection}>
              选择连接
            </Button>
          </Space>
        </div>
      </div>

      {/* 可滚动的中间内容区域 */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden bg-gray-50" style={{ maxHeight: `calc(${containerHeight}px)` }}>
        {!dbConnection ? (
          <div className="text-center py-20 px-8 text-gray-400 bg-gradient-to-br from-gray-50 to-slate-50 border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors duration-300">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
              <DatabaseOutlined className="text-2xl text-gray-400" />
            </div>
            <div className="text-lg mb-2 font-medium text-gray-600">暂无数据库连接配置</div>
            <div className="text-sm text-gray-500">请点击"新增连接"或"选择连接"添加配置</div>
          </div>
        ) : (
          <div className="bg-white border border-gray-200 shadow-sm overflow-hidden">
            <Card className="m-0 border-0 shadow-none" styles={{ body: { padding: '24px' } }}>
              <div className="space-y-6">
                {/* 基本信息区域 */}
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border border-blue-100">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                        <DatabaseOutlined className="text-white text-lg" />
                      </div>
                      <div>
                        <h5 className="m-0 text-lg font-semibold text-gray-800">{dbConnection.name}</h5>
                        <div className="flex items-center gap-2 mt-1">
                          <Tag color={getDbTypeColor(dbConnection.db_type)} className="text-xs">
                            {dbConnection.db_type.toUpperCase()}
                          </Tag>
                          <span className="text-gray-500 text-sm">
                            {dbConnection.host}:{dbConnection.port}
                          </span>
                        </div>
                      </div>
                    </div>
                    <Space>
                      <Tooltip title="编辑连接">
                        <Button type="text" size="small" icon={<EditOutlined />} onClick={onEditDbConnection} className="hover:bg-blue-100 hover:text-blue-600 transition-colors duration-200">
                          编辑
                        </Button>
                      </Tooltip>
                      <Popconfirm
                        title="确认删除"
                        description={`确定要删除数据库连接"${dbConnection.name}"吗？`}
                        onConfirm={handleDeleteDbConnection}
                        okText="确认"
                        cancelText="取消"
                        placement="topRight"
                      >
                        <Tooltip title="删除连接">
                          <Button type="text" size="small" danger icon={<DeleteOutlined />} className="hover:bg-red-50 hover:text-red-600 transition-colors duration-200">
                            删除
                          </Button>
                        </Tooltip>
                      </Popconfirm>
                    </Space>
                  </div>
                </div>

                {/* 详细信息区域 */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <div className="text-sm text-gray-500 mb-1">用户名</div>
                    <div className="font-medium text-gray-800">{dbConnection.user}</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <div className="text-sm text-gray-500 mb-1">数据库名</div>
                    <div className="font-medium text-gray-800">{dbConnection.database}</div>
                  </div>
                  {dbConnection.db_type.toLowerCase() === 'mysql' && (
                    <>
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <div className="text-sm text-gray-500 mb-1">SSL连接</div>
                        <div className="font-medium text-gray-800">
                          <Tag color={dbConnection.use_ssl ? 'green' : 'red'}>{dbConnection.use_ssl ? '启用' : '禁用'}</Tag>
                        </div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <div className="text-sm text-gray-500 mb-1">服务器时区</div>
                        <div className="font-medium text-gray-800">{dbConnection.server_timezone || '默认'}</div>
                      </div>
                    </>
                  )}
                  {dbConnection.db_type.toLowerCase() === 'oracle' && (
                    <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                      <div className="text-sm text-gray-500 mb-1">连接方式</div>
                      <div className="font-medium text-gray-800">
                        <Tag color="blue">{dbConnection.connect_method?.toUpperCase() || 'SID'}</Tag>
                      </div>
                    </div>
                  )}
                </div>

                {/* 时间信息 */}
                <div className="border-t border-gray-200 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
                    <div>
                      <span className="font-medium">创建时间：</span>
                      {new Date(dbConnection.create_time).toLocaleString()}
                    </div>
                    <div>
                      <span className="font-medium">更新时间：</span>
                      {new Date(dbConnection.update_time).toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>

      {/* 选择数据库连接抽屉 */}
      <Drawer
        title={
          <div className="flex justify-between items-center w-full">
            <span>选择数据库连接</span>
            <Button type="primary" icon={<CheckOutlined />} onClick={handleDrawerSelectionConfirm} className="w-32">
              {/* 确认选择 {selectedRowFromChild && `(${selectedRowFromChild.name})`} */}
              确认选择
            </Button>
          </div>
        }
        styles={{ body: { padding: '12px' } }}
        width="80%"
        open={drawerVisible}
        onClose={handleDrawerClose}
        maskClosable={false}
      >
        <DBConnectionTable contentHeight={800} isSelectionMode={true} selectedRows={dbConnection ? [dbConnection] : []} onSelectionConfirm={handleSelectionChange} />
      </Drawer>
    </div>
  );
};

export default DatabaseConfigTab;
